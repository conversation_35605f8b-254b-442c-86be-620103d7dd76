import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import signal
from scipy.fft import fft, fftfreq
import os

class SSVEPAnalyzer:
    def __init__(self, sampling_rate=250):
        """
        Initialize SSVEP analyzer

        Args:
            sampling_rate (int): Sampling rate in Hz (default 250 for OpenBCI)
        """
        self.sampling_rate = sampling_rate
        self.eeg_channels = range(1,8)  # EEG channel indices in BrainFlow format

    def load_brainflow_data(self, filepath):
        """
        Load BrainFlow CSV data

        Args:
            filepath (str): Path to CSV file

        Returns:
            tuple: (eeg_data, timestamps)
        """
        print(f"Loading data from: {filepath}")
        data = pd.read_csv(filepath, header=None, sep='\t')
        print(data.shape)
        # Extract EEG channels (columns 1-8 in BrainFlow format)
        eeg_data = data.iloc[:, self.eeg_channels].values.T  # Transpose to get channels x samples
        timestamps = data.iloc[:, 0].values  # First column is timestamp

        print(f"Data shape: {eeg_data.shape} (channels x samples)")
        print(f"Duration: {len(timestamps) / self.sampling_rate:.2f} seconds")

        return eeg_data, timestamps

    def preprocess_data(self, eeg_data, trim_seconds=10):
        """
        Preprocess EEG data: remove artifacts from start/end and apply filtering

        Args:
            eeg_data (np.array): EEG data (channels x samples)
            trim_seconds (int): Seconds to trim from start and end

        Returns:
            np.array: Preprocessed EEG data
        """
        print(f"Preprocessing data - trimming {trim_seconds}s from start and end")

        # Trim data
        trim_samples = int(trim_seconds * self.sampling_rate)
        if eeg_data.shape[1] > 2 * trim_samples:
            eeg_data = eeg_data[:, trim_samples:-trim_samples]
        else:
            print("Warning: Data too short for trimming, using full data")

        # Apply bandpass filter (1-50 Hz to remove DC and high-frequency noise)
        nyquist = self.sampling_rate / 2
        low_freq = 1.0 / nyquist
        high_freq = 50.0 / nyquist

        b, a = signal.butter(4, [low_freq, high_freq], btype='band')
        filtered_data = np.zeros_like(eeg_data)

        for ch in range(eeg_data.shape[0]):
            filtered_data[ch, :] = signal.filtfilt(b, a, eeg_data[ch, :])

        print(f"Filtered data shape: {filtered_data.shape}")
        return filtered_data

    def compute_psd(self, eeg_data, nperseg=None):
        """
        Compute Power Spectral Density using Welch's method

        Args:
            eeg_data (np.array): EEG data (channels x samples)
            nperseg (int): Length of each segment for Welch's method

        Returns:
            tuple: (frequencies, psd)
        """
        if nperseg is None:
            nperseg = min(2048, eeg_data.shape[1] // 4)

        frequencies, psd = signal.welch(eeg_data, fs=self.sampling_rate,
                                       nperseg=nperseg, axis=1)

        return frequencies, psd

    def extract_ssvep_power(self, frequencies, psd, target_freq, bandwidth=0.5):
        """
        Extract power at target SSVEP frequency

        Args:
            frequencies (np.array): Frequency array
            psd (np.array): Power spectral density (channels x frequencies)
            target_freq (float): Target SSVEP frequency
            bandwidth (float): Bandwidth around target frequency

        Returns:
            np.array: Power at target frequency for each channel
        """
        freq_mask = (frequencies >= target_freq - bandwidth) & \
                   (frequencies <= target_freq + bandwidth)

        # Sum power in the frequency band
        ssvep_power = np.sum(psd[:, freq_mask], axis=1)

        return ssvep_power

    def compute_snr(self, frequencies, psd, target_freq, bandwidth=0.5, noise_bandwidth=2.0):
        """
        Compute Signal-to-Noise Ratio for SSVEP

        Args:
            frequencies (np.array): Frequency array
            psd (np.array): Power spectral density
            target_freq (float): Target SSVEP frequency
            bandwidth (float): Signal bandwidth
            noise_bandwidth (float): Noise bandwidth around signal

        Returns:
            np.array: SNR for each channel
        """
        # Signal power
        signal_power = self.extract_ssvep_power(frequencies, psd, target_freq, bandwidth)

        # Noise power (excluding signal band)
        noise_freq_low = target_freq - noise_bandwidth
        noise_freq_high = target_freq + noise_bandwidth
        signal_freq_low = target_freq - bandwidth
        signal_freq_high = target_freq + bandwidth

        noise_mask = ((frequencies >= noise_freq_low) & (frequencies < signal_freq_low)) | \
                    ((frequencies > signal_freq_high) & (frequencies <= noise_freq_high))

        noise_power = np.mean(psd[:, noise_mask], axis=1) * (2 * bandwidth / (frequencies[1] - frequencies[0]))

        # Avoid division by zero
        snr = np.where(noise_power > 0, signal_power / noise_power, 0)

        return snr

    def plot_psd(self, frequencies, psd, target_freqs, title="Power Spectral Density"):
        """
        Plot Power Spectral Density with SSVEP frequency markers

        Args:
            frequencies (np.array): Frequency array
            psd (np.array): Power spectral density
            target_freqs (list): List of target SSVEP frequencies
            title (str): Plot title
        """
        plt.figure(figsize=(12, 8))

        # Plot PSD for each channel
        for ch in range(psd.shape[0]):
            plt.subplot(2, 4, ch + 1)
            plt.semilogy(frequencies, psd[ch, :])
            plt.xlabel('Frequency (Hz)')
            plt.ylabel('Power (µV²/Hz)')
            plt.title(f'Channel {ch + 1}')
            plt.grid(True, alpha=0.3)
            plt.xlim(0, 50)

            # Mark target frequencies
            for freq in target_freqs:
                plt.axvline(freq, color='red', linestyle='--', alpha=0.7, label=f'{freq}Hz')

            if ch == 0:  # Add legend only to first subplot
                plt.legend()

        plt.suptitle(title)
        plt.tight_layout()
        plt.show()

    def analyze_ssvep_response(self, eeg_data, target_freqs, condition_name):
        """
        Analyze SSVEP response for given frequencies

        Args:
            eeg_data (np.array): Preprocessed EEG data
            target_freqs (list): List of target frequencies
            condition_name (str): Name of the condition

        Returns:
            dict: Analysis results
        """
        print(f"\n=== SSVEP Analysis for {condition_name} ===")

        # Compute PSD
        frequencies, psd = self.compute_psd(eeg_data)

        results = {
            'condition': condition_name,
            'frequencies': frequencies,
            'psd': psd,
            'target_analysis': {}
        }

        # Analyze each target frequency
        for target_freq in target_freqs:
            print(f"\nAnalyzing {target_freq} Hz:")

            # Extract power and SNR
            ssvep_power = self.extract_ssvep_power(frequencies, psd, target_freq)
            snr = self.compute_snr(frequencies, psd, target_freq)

            # Find best channel (highest SNR)
            best_channel = np.argmax(snr)

            print(f"  Best channel: {best_channel + 1} (SNR: {snr[best_channel]:.2f})")
            print(f"  Power across channels: {ssvep_power}")
            print(f"  SNR across channels: {snr}")

            results['target_analysis'][target_freq] = {
                'power': ssvep_power,
                'snr': snr,
                'best_channel': best_channel,
                'best_snr': snr[best_channel]
            }

        return results

    def compare_conditions(self, results_list):
        """
        Compare SSVEP responses across different conditions

        Args:
            results_list (list): List of analysis results from different conditions
        """
        print("\n" + "="*60)
        print("SSVEP COMPARISON ACROSS CONDITIONS")
        print("="*60)

        # Extract all target frequencies
        all_freqs = set()
        for results in results_list:
            all_freqs.update(results['target_analysis'].keys())
        all_freqs = sorted(list(all_freqs))

        # Create comparison table
        print(f"\n{'Condition':<20} {'Frequency':<10} {'Best Ch':<8} {'Best SNR':<10} {'Avg SNR':<10}")
        print("-" * 60)

        for results in results_list:
            condition = results['condition']
            for freq in all_freqs:
                if freq in results['target_analysis']:
                    analysis = results['target_analysis'][freq]
                    best_ch = analysis['best_channel'] + 1
                    best_snr = analysis['best_snr']
                    avg_snr = np.mean(analysis['snr'])
                    print(f"{condition:<20} {freq:<10} {best_ch:<8} {best_snr:<10.2f} {avg_snr:<10.2f}")
                else:
                    print(f"{condition:<20} {freq:<10} {'N/A':<8} {'N/A':<10} {'N/A':<10}")

        # Plot comparison
        self.plot_comparison(results_list, all_freqs)

    def plot_comparison(self, results_list, target_freqs):
        """
        Plot comparison of SSVEP responses

        Args:
            results_list (list): List of analysis results
            target_freqs (list): List of target frequencies
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # Plot 1: SNR comparison
        ax1 = axes[0, 0]
        conditions = [r['condition'] for r in results_list]
        x_pos = np.arange(len(conditions))

        for i, freq in enumerate(target_freqs):
            snr_values = []
            for results in results_list:
                if freq in results['target_analysis']:
                    snr_values.append(results['target_analysis'][freq]['best_snr'])
                else:
                    snr_values.append(0)

            ax1.bar(x_pos + i*0.25, snr_values, 0.25, label=f'{freq} Hz')

        ax1.set_xlabel('Condition')
        ax1.set_ylabel('Best SNR')
        ax1.set_title('SSVEP SNR Comparison')
        ax1.set_xticks(x_pos + 0.125)
        ax1.set_xticklabels(conditions)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Plot 2: Power spectral density overlay
        ax2 = axes[0, 1]
        colors = ['blue', 'red', 'green', 'orange']
        for i, results in enumerate(results_list):
            # Average PSD across channels
            avg_psd = np.mean(results['psd'], axis=0)
            ax2.semilogy(results['frequencies'], avg_psd,
                        color=colors[i % len(colors)], label=results['condition'])

        for freq in target_freqs:
            ax2.axvline(freq, color='black', linestyle='--', alpha=0.5)

        ax2.set_xlabel('Frequency (Hz)')
        ax2.set_ylabel('Power (µV²/Hz)')
        ax2.set_title('Average PSD Comparison')
        ax2.set_xlim(5, 25)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Plot 3: Channel-wise SNR heatmap for first target frequency
        if target_freqs:
            ax3 = axes[1, 0]
            freq = target_freqs[0]
            snr_matrix = []

            for results in results_list:
                if freq in results['target_analysis']:
                    snr_matrix.append(results['target_analysis'][freq]['snr'])
                else:
                    snr_matrix.append(np.zeros(1))

            snr_matrix = np.array(snr_matrix)
            im = ax3.imshow(snr_matrix, cmap='viridis', aspect='auto')
            ax3.set_xlabel('Channel')
            ax3.set_ylabel('Condition')
            ax3.set_title(f'SNR Heatmap ({freq} Hz)')
            ax3.set_xticks(range(8))
            ax3.set_xticklabels([f'Ch{i+1}' for i in range(8)])
            ax3.set_yticks(range(len(conditions)))
            ax3.set_yticklabels(conditions)
            plt.colorbar(im, ax=ax3)

        # Plot 4: Summary statistics
        ax4 = axes[1, 1]
        ax4.axis('off')

        # Create summary text
        summary_text = "SSVEP Analysis Summary\n\n"
        for results in results_list:
            summary_text += f"{results['condition']}:\n"
            for freq in target_freqs:
                if freq in results['target_analysis']:
                    analysis = results['target_analysis'][freq]
                    summary_text += f"  {freq}Hz: Ch{analysis['best_channel']+1} (SNR={analysis['best_snr']:.2f})\n"
            summary_text += "\n"

        ax4.text(0.1, 0.9, summary_text, transform=ax4.transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace')

        plt.tight_layout()
        plt.show()


def main():
    """
    Main analysis function to process all SSVEP data files
    """
    print("Starting SSVEP Analysis")
    print("="*50)

    # Initialize analyzer
    analyzer = SSVEPAnalyzer(sampling_rate=250)  # Assuming 250 Hz sampling rate

    # Define data files and their corresponding conditions
    data_files = {
        '15Hz_active': 'data/BrainFlow-RAW_15Hz-SoftPulseFlex_active.csv',
        '15Hz_passive': 'data/BrainFlow-RAW_15Hz-SoftPulseFlex_passive.csv'
    }

    # Define target frequencies for each condition
    target_frequencies = {

        '15Hz_active': [15.0],
        '15Hz_passive': [15.0]
    }

    # Store results for comparison
    all_results = []

    # Process each data file
    for condition, filepath in data_files.items():
        try:
            print(f"\nProcessing {condition}...")

            # Check if file exists
            if not os.path.exists(filepath):
                print(f"Warning: File {filepath} not found, skipping...")
                continue

            # Load data
            eeg_data, timestamps = analyzer.load_brainflow_data(filepath)

            # Preprocess data (remove 10s from start and end, apply filtering)
            processed_data = analyzer.preprocess_data(eeg_data, trim_seconds=10)

            # Analyze SSVEP response
            target_freqs = target_frequencies[condition]
            results = analyzer.analyze_ssvep_response(processed_data, target_freqs, condition)

            # Plot PSD for this condition
            analyzer.plot_psd(results['frequencies'], results['psd'],
                            target_freqs, title=f"PSD - {condition}")

            all_results.append(results)

        except Exception as e:
            print(f"Error processing {condition}: {str(e)}")
            continue

    # Compare all conditions
    if len(all_results) > 1:
        print("\nComparing all conditions...")
        analyzer.compare_conditions(all_results)

    # Additional analysis: Check for harmonics
    print("\n" + "="*60)
    print("HARMONIC ANALYSIS")
    print("="*60)

    for results in all_results:
        condition = results['condition']
        print(f"\n{condition}:")

        for target_freq in results['target_analysis'].keys():
            # Check 2nd harmonic
            harmonic_freq = target_freq * 2
            if harmonic_freq <= 50:  # Within our analysis range
                frequencies = results['frequencies']
                psd = results['psd']

                harmonic_power = analyzer.extract_ssvep_power(frequencies, psd, harmonic_freq)
                harmonic_snr = analyzer.compute_snr(frequencies, psd, harmonic_freq)

                best_ch_harmonic = np.argmax(harmonic_snr)

                print(f"  {target_freq}Hz fundamental vs {harmonic_freq}Hz harmonic:")
                print(f"    Fundamental SNR (best ch): {results['target_analysis'][target_freq]['best_snr']:.2f}")
                print(f"    Harmonic SNR (best ch): {harmonic_snr[best_ch_harmonic]:.2f}")

    print("\n" + "="*60)
    print("ANALYSIS COMPLETE")
    print("="*60)

    # Summary recommendations
    print("\nSUMMARY & RECOMMENDATIONS:")
    print("-" * 30)

    if all_results:
        # Find best performing condition and frequency
        best_snr = 0
        best_condition = ""
        best_freq = 0
        best_channel = 0

        for results in all_results:
            for freq, analysis in results['target_analysis'].items():
                if analysis['best_snr'] > best_snr:
                    best_snr = analysis['best_snr']
                    best_condition = results['condition']
                    best_freq = freq
                    best_channel = analysis['best_channel'] + 1

        print(f"Best SSVEP response: {best_condition} at {best_freq}Hz")
        print(f"Best channel: {best_channel} (SNR: {best_snr:.2f})")

        # Recommendations
        if best_snr > 3.0:
            print("✓ Strong SSVEP response detected - good for BCI applications")
        elif best_snr > 1.5:
            print("⚠ Moderate SSVEP response - may work with optimization")
        else:
            print("✗ Weak SSVEP response - consider electrode placement or stimulus parameters")

        # Compare active vs passive
        active_results = [r for r in all_results if 'active' in r['condition']]
        passive_results = [r for r in all_results if 'passive' in r['condition'] or 'control' in r['condition']]

        if active_results and passive_results:
            active_snr = np.mean([list(r['target_analysis'].values())[0]['best_snr'] for r in active_results])
            passive_snr = np.mean([list(r['target_analysis'].values())[0]['best_snr'] for r in passive_results])

            print(f"\nActive vs Passive comparison:")
            print(f"Average active SNR: {active_snr:.2f}")
            print(f"Average passive SNR: {passive_snr:.2f}")

            if active_snr > passive_snr * 1.5:
                print("✓ Clear difference between active and passive conditions")
            else:
                print("⚠ Limited difference between active and passive conditions")


if __name__ == "__main__":
    main()